{"version": 3, "sources": [], "sections": [{"offset": {"line": 3, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 11, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Internship-Projects/The-Salty-Devs/client/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,IAAA,iLAAG,EACvB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,kLAAI,GAAG;IAE9B,qBACE,wPAAC;QACC,aAAU;QACV,WAAW,IAAA,mIAAE,EAAC,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 57, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Internship-Projects/The-Salty-Devs/client/src/components/postCard/page.tsx"], "sourcesContent": ["import React from 'react';\r\nimport Link from 'next/link';\r\nimport { Eye, User } from 'lucide-react';\r\nimport { Badge } from '@/components/ui/badge';\r\n\r\ninterface PostCardProps {\r\n  post: {\r\n    id: string;\r\n    title: string;\r\n    excerpt: string;\r\n    date: string;\r\n    slug: string;\r\n    author: string;\r\n    category?: string;\r\n    tags?: string[];\r\n    viewCount?: number;\r\n  };\r\n}\r\n\r\nconst PostCard = ({ post }: PostCardProps) => {\r\n  const {\r\n    title,\r\n    excerpt,\r\n    date,\r\n    slug,\r\n    author,\r\n    category = 'General',\r\n    tags = [],\r\n    viewCount = 0,\r\n  } = post;\r\n  return (\r\n    <Link href={`/posts/${slug}`}>\r\n      <article className=\"group bg-card border border-border p-6 hover:gradient-border transition-all duration-300 h-full flex flex-col\">\r\n        <div className=\"space-y-4 flex-1\">\r\n          {/* Category and View Count */}\r\n          <div className=\"flex items-center justify-between\">\r\n            <Badge variant=\"secondary\" className=\"text-xs\">\r\n              {category}\r\n            </Badge>\r\n            {viewCount > 0 && (\r\n              <div className=\"flex items-center space-x-1 text-xs text-muted-foreground\">\r\n                <Eye className=\"h-3 w-3\" />\r\n                <span>{viewCount.toLocaleString()}</span>\r\n              </div>\r\n            )}\r\n          </div>\r\n\r\n          {/* Title */}\r\n          <h2 className=\"text-xl font-bold text-card-foreground group-hover:gradient-text transition-colors line-clamp-2\">\r\n            {title}\r\n          </h2>\r\n\r\n          {/* Excerpt */}\r\n          <p className=\"text-muted-foreground leading-relaxed line-clamp-3 flex-1\">\r\n            {excerpt}\r\n          </p>\r\n        </div>\r\n\r\n        {/* Footer */}\r\n        <div className=\"mt-4 pt-4 border-t border-border space-y-3\">\r\n          {/* Author and Date */}\r\n          <div className=\"flex items-center justify-between text-sm\">\r\n            <div className=\"flex items-center space-x-2 text-secondary-foreground\">\r\n              <User className=\"h-3 w-3\" />\r\n              <span>{author}</span>\r\n            </div>\r\n            <time className=\"text-secondary-foreground font-mono\">{date}</time>\r\n          </div>\r\n\r\n          {/* Tags */}\r\n          {tags.length > 0 && (\r\n            <div className=\"flex flex-wrap gap-2\">\r\n              {tags.slice(0, 3).map((tag) => (\r\n                <Badge key={tag} variant=\"outline\" className=\"text-xs\">\r\n                  {tag}\r\n                </Badge>\r\n              ))}\r\n              {tags.length > 3 && (\r\n                <Badge variant=\"outline\" className=\"text-xs\">\r\n                  +{tags.length - 3}\r\n                </Badge>\r\n              )}\r\n            </div>\r\n          )}\r\n        </div>\r\n      </article>\r\n    </Link>\r\n  );\r\n};\r\n\r\nexport default PostCard;\r\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAAA;AACA;;;;;AAgBA,MAAM,WAAW,CAAC,EAAE,IAAI,EAAiB;IACvC,MAAM,EACJ,KAAK,EACL,OAAO,EACP,IAAI,EACJ,IAAI,EACJ,MAAM,EACN,WAAW,SAAS,EACpB,OAAO,EAAE,EACT,YAAY,CAAC,EACd,GAAG;IACJ,qBACE,wPAAC,iLAAI;QAAC,MAAM,CAAC,OAAO,EAAE,MAAM;kBAC1B,cAAA,wPAAC;YAAQ,WAAU;;8BACjB,wPAAC;oBAAI,WAAU;;sCAEb,wPAAC;4BAAI,WAAU;;8CACb,wPAAC,oJAAK;oCAAC,SAAQ;oCAAY,WAAU;8CAClC;;;;;;gCAEF,YAAY,mBACX,wPAAC;oCAAI,WAAU;;sDACb,wPAAC,iNAAG;4CAAC,WAAU;;;;;;sDACf,wPAAC;sDAAM,UAAU,cAAc;;;;;;;;;;;;;;;;;;sCAMrC,wPAAC;4BAAG,WAAU;sCACX;;;;;;sCAIH,wPAAC;4BAAE,WAAU;sCACV;;;;;;;;;;;;8BAKL,wPAAC;oBAAI,WAAU;;sCAEb,wPAAC;4BAAI,WAAU;;8CACb,wPAAC;oCAAI,WAAU;;sDACb,wPAAC,oNAAI;4CAAC,WAAU;;;;;;sDAChB,wPAAC;sDAAM;;;;;;;;;;;;8CAET,wPAAC;oCAAK,WAAU;8CAAuC;;;;;;;;;;;;wBAIxD,KAAK,MAAM,GAAG,mBACb,wPAAC;4BAAI,WAAU;;gCACZ,KAAK,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,oBACrB,wPAAC,oJAAK;wCAAW,SAAQ;wCAAU,WAAU;kDAC1C;uCADS;;;;;gCAIb,KAAK,MAAM,GAAG,mBACb,wPAAC,oJAAK;oCAAC,SAAQ;oCAAU,WAAU;;wCAAU;wCACzC,KAAK,MAAM,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASlC;uCAEe", "debugId": null}}, {"offset": {"line": 239, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Internship-Projects/The-Salty-Devs/client/src/app/page.tsx"], "sourcesContent": ["import React from 'react';\r\nimport PostCard from '@/components/postCard/page';\r\n\r\nconst mockPosts = [\r\n  {\r\n    slug: 'type-safe-api-with-prisma',\r\n    title: 'Building a Type-Safe API with Prisma and tRPC',\r\n    date: 'August 18, 2025',\r\n    excerpt:\r\n      \"Moving beyond traditional REST. How combining Prisma's database safety with tRPC's end-to-end type safety can eliminate entire classes of bugs...\",\r\n  },\r\n  {\r\n    slug: 'the-new-wave-of-css',\r\n    title: 'The New Wave of CSS',\r\n    date: 'July 22, 2025',\r\n    excerpt:\r\n      'A look at modern CSS features that are changing the way we build layouts and design systems: container queries, the `:has()` selector, and cascade layers.',\r\n  },\r\n  {\r\n    slug: 'server-components-in-nextjs-14',\r\n    title: 'Server Components in Next.js 14: A Practical Guide',\r\n    date: 'June 15, 2025',\r\n    excerpt:\r\n      'Moving beyond the theory. A step-by-step guide to refactoring a client-side rendered app to leverage the power of React Server Components for better performance.',\r\n  },\r\n];\r\n\r\nexport default function Home() {\r\n  return (\r\n    <main className=\"container mx-auto px-4 py-8\">\r\n      <div className=\"text-center mb-12\">\r\n        <h1 className=\"text-4xl md:text-6xl font-bold mb-4\">\r\n          Welcome to Our Blog\r\n        </h1>\r\n        <p className=\"text-xl text-muted-foreground max-w-2xl mx-auto\">\r\n          Discover insights, tutorials, and thoughts on web development, design,\r\n          and technology.\r\n        </p>\r\n      </div>\r\n\r\n      <div className=\"grid gap-6 md:grid-cols-2 lg:grid-cols-3\">\r\n        {mockPosts.map((post) => (\r\n          <PostCard key={post.id} post={post} />\r\n        ))}\r\n      </div>\r\n    </main>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;AACA;;;AAEA,MAAM,YAAY;IAChB;QACE,MAAM;QACN,OAAO;QACP,MAAM;QACN,SACE;IACJ;IACA;QACE,MAAM;QACN,OAAO;QACP,MAAM;QACN,SACE;IACJ;IACA;QACE,MAAM;QACN,OAAO;QACP,MAAM;QACN,SACE;IACJ;CACD;AAEc,SAAS;IACtB,qBACE,wPAAC;QAAK,WAAU;;0BACd,wPAAC;gBAAI,WAAU;;kCACb,wPAAC;wBAAG,WAAU;kCAAsC;;;;;;kCAGpD,wPAAC;wBAAE,WAAU;kCAAkD;;;;;;;;;;;;0BAMjE,wPAAC;gBAAI,WAAU;0BACZ,UAAU,GAAG,CAAC,CAAC,qBACd,wPAAC,2JAAQ;wBAAe,MAAM;uBAAf,KAAK,EAAE;;;;;;;;;;;;;;;;AAKhC", "debugId": null}}]}