{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Internship-Projects/The-Salty-Devs/client/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from 'clsx';\nimport { twMerge } from 'tailwind-merge';\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,IAAA,gLAAO,EAAC,IAAA,uJAAI,EAAC;AACtB", "debugId": null}}, {"offset": {"line": 19, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Internship-Projects/The-Salty-Devs/client/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,IAAA,iLAAG,EACxB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,kLAAI,GAAG;IAE9B,qBACE,wPAAC;QACC,aAAU;QACV,WAAW,IAAA,mIAAE,EAAC,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 76, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Internship-Projects/The-Salty-Devs/client/src/components/header/page.tsx"], "sourcesContent": ["import { Link, useLocation } from 'react-router-dom';\r\nimport { Button } from '@/components/ui/button';\r\nimport { Sun, Moon } from 'lucide-react';\r\n\r\nconst Header = () => {\r\n  const location = useLocation();\r\n  const isAdmin = location.pathname.startsWith('/admin');\r\n\r\n  if (isAdmin) {\r\n    return null; // Admin pages have their own header\r\n  }\r\n\r\n  const navigation = [\r\n    { name: 'Articles', href: '/' },\r\n    { name: 'Categories', href: '/categories' },\r\n    { name: 'About', href: '/about' },\r\n  ];\r\n\r\n  const toggleTheme = () => {\r\n    document.documentElement.classList.toggle('light');\r\n  };\r\n\r\n  return (\r\n    <header className=\"fixed top-0 left-0 right-0 z-50 bg-background/80 backdrop-blur-sm border-b border-border\">\r\n      <div className=\"max-w-6xl mx-auto px-6 h-16 flex items-center justify-between\">\r\n        {/* Logo */}\r\n        <Link\r\n          to=\"/\"\r\n          className=\"font-bold text-xl text-foreground hover:gradient-text\"\r\n        >\r\n          The Salty Devs\r\n        </Link>\r\n\r\n        {/* Center Navigation */}\r\n        <nav className=\"hidden md:flex items-center space-x-8\">\r\n          {navigation.map((item) => (\r\n            <Link\r\n              key={item.name}\r\n              to={item.href}\r\n              className={`text-sm font-medium transition-colors ${\r\n                location.pathname === item.href\r\n                  ? 'gradient-text'\r\n                  : 'text-muted-foreground hover:text-foreground'\r\n              }`}\r\n            >\r\n              {item.name}\r\n            </Link>\r\n          ))}\r\n        </nav>\r\n\r\n        {/* Right Section */}\r\n        <div className=\"flex items-center space-x-4\">\r\n          <Button\r\n            variant=\"ghost\"\r\n            size=\"icon\"\r\n            onClick={toggleTheme}\r\n            className=\"h-9 w-9\"\r\n          >\r\n            <Sun className=\"h-4 w-4 rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0\" />\r\n            <Moon className=\"absolute h-4 w-4 rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100\" />\r\n            <span className=\"sr-only\">Toggle theme</span>\r\n          </Button>\r\n\r\n          <Link to=\"/login\">\r\n            <Button variant=\"ghost\" size=\"sm\">\r\n              Login\r\n            </Button>\r\n          </Link>\r\n\r\n          <Link to=\"/signup\">\r\n            <Button\r\n              size=\"sm\"\r\n              className=\"gradient-text bg-primary text-primary-foreground hover:bg-primary/90\"\r\n            >\r\n              Sign Up\r\n            </Button>\r\n          </Link>\r\n        </div>\r\n      </div>\r\n    </header>\r\n  );\r\n};\r\n\r\nexport default Header;\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AAAA;;;;;AAEA,MAAM,SAAS;IACb,MAAM,WAAW,IAAA,gLAAW;IAC5B,MAAM,UAAU,SAAS,QAAQ,CAAC,UAAU,CAAC;IAE7C,IAAI,SAAS;QACX,OAAO,MAAM,oCAAoC;IACnD;IAEA,MAAM,aAAa;QACjB;YAAE,MAAM;YAAY,MAAM;QAAI;QAC9B;YAAE,MAAM;YAAc,MAAM;QAAc;QAC1C;YAAE,MAAM;YAAS,MAAM;QAAS;KACjC;IAED,MAAM,cAAc;QAClB,SAAS,eAAe,CAAC,SAAS,CAAC,MAAM,CAAC;IAC5C;IAEA,qBACE,wPAAC;QAAO,WAAU;kBAChB,cAAA,wPAAC;YAAI,WAAU;;8BAEb,wPAAC,yKAAI;oBACH,IAAG;oBACH,WAAU;8BACX;;;;;;8BAKD,wPAAC;oBAAI,WAAU;8BACZ,WAAW,GAAG,CAAC,CAAC,qBACf,wPAAC,yKAAI;4BAEH,IAAI,KAAK,IAAI;4BACb,WAAW,CAAC,sCAAsC,EAChD,SAAS,QAAQ,KAAK,KAAK,IAAI,GAC3B,kBACA,+CACJ;sCAED,KAAK,IAAI;2BARL,KAAK,IAAI;;;;;;;;;;8BAcpB,wPAAC;oBAAI,WAAU;;sCACb,wPAAC,sJAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,SAAS;4BACT,WAAU;;8CAEV,wPAAC,iNAAG;oCAAC,WAAU;;;;;;8CACf,wPAAC,oNAAI;oCAAC,WAAU;;;;;;8CAChB,wPAAC;oCAAK,WAAU;8CAAU;;;;;;;;;;;;sCAG5B,wPAAC,yKAAI;4BAAC,IAAG;sCACP,cAAA,wPAAC,sJAAM;gCAAC,SAAQ;gCAAQ,MAAK;0CAAK;;;;;;;;;;;sCAKpC,wPAAC,yKAAI;4BAAC,IAAG;sCACP,cAAA,wPAAC,sJAAM;gCACL,MAAK;gCACL,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQb;uCAEe", "debugId": null}}, {"offset": {"line": 234, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Internship-Projects/The-Salty-Devs/client/src/components/ui/logo.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const Logo = registerClientReference(\n    function() { throw new Error(\"Attempted to call Logo() from the server but Logo is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/client/src/components/ui/logo.tsx <module evaluation>\",\n    \"Logo\",\n);\nexport const LogoBrandDownload = registerClientReference(\n    function() { throw new Error(\"Attempted to call LogoBrandDownload() from the server but LogoBrandDownload is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/client/src/components/ui/logo.tsx <module evaluation>\",\n    \"LogoBrandDownload\",\n);\nexport const LogoImage = registerClientReference(\n    function() { throw new Error(\"Attempted to call LogoImage() from the server but LogoImage is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/client/src/components/ui/logo.tsx <module evaluation>\",\n    \"LogoImage\",\n);\nexport const LogoImageDesktop = registerClientReference(\n    function() { throw new Error(\"Attempted to call LogoImageDesktop() from the server but LogoImageDesktop is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/client/src/components/ui/logo.tsx <module evaluation>\",\n    \"LogoImageDesktop\",\n);\nexport const LogoImageMobile = registerClientReference(\n    function() { throw new Error(\"Attempted to call LogoImageMobile() from the server but LogoImageMobile is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/client/src/components/ui/logo.tsx <module evaluation>\",\n    \"LogoImageMobile\",\n);\nexport const LogoText = registerClientReference(\n    function() { throw new Error(\"Attempted to call LogoText() from the server but LogoText is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/client/src/components/ui/logo.tsx <module evaluation>\",\n    \"LogoText\",\n);\nexport const LogoTextDesktop = registerClientReference(\n    function() { throw new Error(\"Attempted to call LogoTextDesktop() from the server but LogoTextDesktop is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/client/src/components/ui/logo.tsx <module evaluation>\",\n    \"LogoTextDesktop\",\n);\nexport const LogoTextMobile = registerClientReference(\n    function() { throw new Error(\"Attempted to call LogoTextMobile() from the server but LogoTextMobile is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/client/src/components/ui/logo.tsx <module evaluation>\",\n    \"LogoTextMobile\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;;;;;;;;;;;;;;;AACvE;;AACO,MAAM,OAAO,IAAA,kRAAuB,EACvC;IAAa,MAAM,IAAI,MAAM;AAAwN,GACrP,mEACA;AAEG,MAAM,oBAAoB,IAAA,kRAAuB,EACpD;IAAa,MAAM,IAAI,MAAM;AAAkP,GAC/Q,mEACA;AAEG,MAAM,YAAY,IAAA,kRAAuB,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,mEACA;AAEG,MAAM,mBAAmB,IAAA,kRAAuB,EACnD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,mEACA;AAEG,MAAM,kBAAkB,IAAA,kRAAuB,EAClD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,mEACA;AAEG,MAAM,WAAW,IAAA,kRAAuB,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAgO,GAC7P,mEACA;AAEG,MAAM,kBAAkB,IAAA,kRAAuB,EAClD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,mEACA;AAEG,MAAM,iBAAiB,IAAA,kRAAuB,EACjD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,mEACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 283, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Internship-Projects/The-Salty-Devs/client/src/components/ui/logo.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const Logo = registerClientReference(\n    function() { throw new Error(\"Attempted to call Logo() from the server but Logo is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/client/src/components/ui/logo.tsx\",\n    \"Logo\",\n);\nexport const LogoBrandDownload = registerClientReference(\n    function() { throw new Error(\"Attempted to call LogoBrandDownload() from the server but LogoBrandDownload is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/client/src/components/ui/logo.tsx\",\n    \"LogoBrandDownload\",\n);\nexport const LogoImage = registerClientReference(\n    function() { throw new Error(\"Attempted to call LogoImage() from the server but LogoImage is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/client/src/components/ui/logo.tsx\",\n    \"LogoImage\",\n);\nexport const LogoImageDesktop = registerClientReference(\n    function() { throw new Error(\"Attempted to call LogoImageDesktop() from the server but LogoImageDesktop is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/client/src/components/ui/logo.tsx\",\n    \"LogoImageDesktop\",\n);\nexport const LogoImageMobile = registerClientReference(\n    function() { throw new Error(\"Attempted to call LogoImageMobile() from the server but LogoImageMobile is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/client/src/components/ui/logo.tsx\",\n    \"LogoImageMobile\",\n);\nexport const LogoText = registerClientReference(\n    function() { throw new Error(\"Attempted to call LogoText() from the server but LogoText is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/client/src/components/ui/logo.tsx\",\n    \"LogoText\",\n);\nexport const LogoTextDesktop = registerClientReference(\n    function() { throw new Error(\"Attempted to call LogoTextDesktop() from the server but LogoTextDesktop is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/client/src/components/ui/logo.tsx\",\n    \"LogoTextDesktop\",\n);\nexport const LogoTextMobile = registerClientReference(\n    function() { throw new Error(\"Attempted to call LogoTextMobile() from the server but LogoTextMobile is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/client/src/components/ui/logo.tsx\",\n    \"LogoTextMobile\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;;;;;;;;;;;;;;;AACvE;;AACO,MAAM,OAAO,IAAA,kRAAuB,EACvC;IAAa,MAAM,IAAI,MAAM;AAAwN,GACrP,+CACA;AAEG,MAAM,oBAAoB,IAAA,kRAAuB,EACpD;IAAa,MAAM,IAAI,MAAM;AAAkP,GAC/Q,+CACA;AAEG,MAAM,YAAY,IAAA,kRAAuB,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,+CACA;AAEG,MAAM,mBAAmB,IAAA,kRAAuB,EACnD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,+CACA;AAEG,MAAM,kBAAkB,IAAA,kRAAuB,EAClD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,+CACA;AAEG,MAAM,WAAW,IAAA,kRAAuB,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAgO,GAC7P,+CACA;AAEG,MAAM,kBAAkB,IAAA,kRAAuB,EAClD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,+CACA;AAEG,MAAM,iBAAiB,IAAA,kRAAuB,EACjD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,+CACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 332, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 340, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Internship-Projects/The-Salty-Devs/client/src/components/footer/page.tsx"], "sourcesContent": ["import { Logo, LogoImage, LogoText } from '@/components/ui/logo';\r\n\r\ninterface MenuItem {\r\n  title: string;\r\n  links: {\r\n    text: string;\r\n    url: string;\r\n  }[];\r\n}\r\n\r\ninterface FooterProps {\r\n  logo?: {\r\n    url: string;\r\n    src: string;\r\n    alt: string;\r\n    title: string;\r\n  };\r\n  tagline?: string;\r\n  menuItems?: MenuItem[];\r\n  copyright?: string;\r\n  bottomLinks?: {\r\n    text: string;\r\n    url: string;\r\n  }[];\r\n}\r\n\r\nconst Footer = ({\r\n  logo = {\r\n    src: 'https://deifkwefumgah.cloudfront.net/shadcnblocks/block/block-1.svg',\r\n    alt: 'blocks for shadcn/ui',\r\n    title: 'Shadcnblocks.com',\r\n    url: 'https://www.shadcnblocks.com',\r\n  },\r\n  tagline = 'Components made easy.',\r\n  menuItems = [\r\n    {\r\n      title: 'Product',\r\n      links: [\r\n        { text: 'Overview', url: '#' },\r\n        { text: 'Pricing', url: '#' },\r\n        { text: 'Marketplace', url: '#' },\r\n        { text: 'Features', url: '#' },\r\n        { text: 'Integrations', url: '#' },\r\n        { text: 'Pricing', url: '#' },\r\n      ],\r\n    },\r\n    {\r\n      title: 'Company',\r\n      links: [\r\n        { text: 'About', url: '#' },\r\n        { text: 'Team', url: '#' },\r\n        { text: 'Blog', url: '#' },\r\n        { text: 'Careers', url: '#' },\r\n        { text: 'Contact', url: '#' },\r\n        { text: 'Privacy', url: '#' },\r\n      ],\r\n    },\r\n    {\r\n      title: 'Resources',\r\n      links: [\r\n        { text: 'Help', url: '#' },\r\n        { text: 'Sales', url: '#' },\r\n        { text: 'Advertise', url: '#' },\r\n      ],\r\n    },\r\n    {\r\n      title: 'Social',\r\n      links: [\r\n        { text: 'Twitter', url: '#' },\r\n        { text: 'Instagram', url: '#' },\r\n        { text: 'LinkedIn', url: '#' },\r\n      ],\r\n    },\r\n  ],\r\n  copyright = '© 2024 Shadcnblocks.com. All rights reserved.',\r\n  bottomLinks = [\r\n    { text: 'Terms and Conditions', url: '#' },\r\n    { text: 'Privacy Policy', url: '#' },\r\n  ],\r\n}: FooterProps) => {\r\n  return (\r\n    <section className=\"py-32\">\r\n      <div className=\"container\">\r\n        <footer>\r\n          <div className=\"grid grid-cols-2 gap-8 lg:grid-cols-6\">\r\n            <div className=\"col-span-2 mb-8 lg:mb-0\">\r\n              <div className=\"flex items-center gap-2 lg:justify-start\">\r\n                <Logo url=\"https://shadcnblocks.com\">\r\n                  <LogoImage\r\n                    src={logo.src}\r\n                    alt={logo.alt}\r\n                    title={logo.title}\r\n                    className=\"h-10\"\r\n                  />\r\n                  <LogoText className=\"text-xl\">{logo.title}</LogoText>\r\n                </Logo>\r\n              </div>\r\n              <p className=\"mt-4 font-bold\">{tagline}</p>\r\n            </div>\r\n            {menuItems.map((section, sectionIdx) => (\r\n              <div key={sectionIdx}>\r\n                <h3 className=\"mb-4 font-bold\">{section.title}</h3>\r\n                <ul className=\"text-muted-foreground space-y-4\">\r\n                  {section.links.map((link, linkIdx) => (\r\n                    <li\r\n                      key={linkIdx}\r\n                      className=\"hover:text-primary font-medium\"\r\n                    >\r\n                      <a href={link.url}>{link.text}</a>\r\n                    </li>\r\n                  ))}\r\n                </ul>\r\n              </div>\r\n            ))}\r\n          </div>\r\n          <div className=\"text-muted-foreground mt-24 flex flex-col justify-between gap-4 border-t pt-8 text-sm font-medium md:flex-row md:items-center\">\r\n            <p>{copyright}</p>\r\n            <ul className=\"flex gap-4\">\r\n              {bottomLinks.map((link, linkIdx) => (\r\n                <li key={linkIdx} className=\"hover:text-primary underline\">\r\n                  <a href={link.url}>{link.text}</a>\r\n                </li>\r\n              ))}\r\n            </ul>\r\n          </div>\r\n        </footer>\r\n      </div>\r\n    </section>\r\n  );\r\n};\r\n\r\nexport default Footer;\r\n"], "names": [], "mappings": ";;;;;AAAA;;;AA0BA,MAAM,SAAS,CAAC,EACd,OAAO;IACL,KAAK;IACL,KAAK;IACL,OAAO;IACP,KAAK;AACP,CAAC,EACD,UAAU,uBAAuB,EACjC,YAAY;IACV;QACE,OAAO;QACP,OAAO;YACL;gBAAE,MAAM;gBAAY,KAAK;YAAI;YAC7B;gBAAE,MAAM;gBAAW,KAAK;YAAI;YAC5B;gBAAE,MAAM;gBAAe,KAAK;YAAI;YAChC;gBAAE,MAAM;gBAAY,KAAK;YAAI;YAC7B;gBAAE,MAAM;gBAAgB,KAAK;YAAI;YACjC;gBAAE,MAAM;gBAAW,KAAK;YAAI;SAC7B;IACH;IACA;QACE,OAAO;QACP,OAAO;YACL;gBAAE,MAAM;gBAAS,KAAK;YAAI;YAC1B;gBAAE,MAAM;gBAAQ,KAAK;YAAI;YACzB;gBAAE,MAAM;gBAAQ,KAAK;YAAI;YACzB;gBAAE,MAAM;gBAAW,KAAK;YAAI;YAC5B;gBAAE,MAAM;gBAAW,KAAK;YAAI;YAC5B;gBAAE,MAAM;gBAAW,KAAK;YAAI;SAC7B;IACH;IACA;QACE,OAAO;QACP,OAAO;YACL;gBAAE,MAAM;gBAAQ,KAAK;YAAI;YACzB;gBAAE,MAAM;gBAAS,KAAK;YAAI;YAC1B;gBAAE,MAAM;gBAAa,KAAK;YAAI;SAC/B;IACH;IACA;QACE,OAAO;QACP,OAAO;YACL;gBAAE,MAAM;gBAAW,KAAK;YAAI;YAC5B;gBAAE,MAAM;gBAAa,KAAK;YAAI;YAC9B;gBAAE,MAAM;gBAAY,KAAK;YAAI;SAC9B;IACH;CACD,EACD,YAAY,+CAA+C,EAC3D,cAAc;IACZ;QAAE,MAAM;QAAwB,KAAK;IAAI;IACzC;QAAE,MAAM;QAAkB,KAAK;IAAI;CACpC,EACW;IACZ,qBACE,wPAAC;QAAQ,WAAU;kBACjB,cAAA,wPAAC;YAAI,WAAU;sBACb,cAAA,wPAAC;;kCACC,wPAAC;wBAAI,WAAU;;0CACb,wPAAC;gCAAI,WAAU;;kDACb,wPAAC;wCAAI,WAAU;kDACb,cAAA,wPAAC,kJAAI;4CAAC,KAAI;;8DACR,wPAAC,uJAAS;oDACR,KAAK,KAAK,GAAG;oDACb,KAAK,KAAK,GAAG;oDACb,OAAO,KAAK,KAAK;oDACjB,WAAU;;;;;;8DAEZ,wPAAC,sJAAQ;oDAAC,WAAU;8DAAW,KAAK,KAAK;;;;;;;;;;;;;;;;;kDAG7C,wPAAC;wCAAE,WAAU;kDAAkB;;;;;;;;;;;;4BAEhC,UAAU,GAAG,CAAC,CAAC,SAAS,2BACvB,wPAAC;;sDACC,wPAAC;4CAAG,WAAU;sDAAkB,QAAQ,KAAK;;;;;;sDAC7C,wPAAC;4CAAG,WAAU;sDACX,QAAQ,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,wBACxB,wPAAC;oDAEC,WAAU;8DAEV,cAAA,wPAAC;wDAAE,MAAM,KAAK,GAAG;kEAAG,KAAK,IAAI;;;;;;mDAHxB;;;;;;;;;;;mCALH;;;;;;;;;;;kCAed,wPAAC;wBAAI,WAAU;;0CACb,wPAAC;0CAAG;;;;;;0CACJ,wPAAC;gCAAG,WAAU;0CACX,YAAY,GAAG,CAAC,CAAC,MAAM,wBACtB,wPAAC;wCAAiB,WAAU;kDAC1B,cAAA,wPAAC;4CAAE,MAAM,KAAK,GAAG;sDAAG,KAAK,IAAI;;;;;;uCADtB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUzB;uCAEe", "debugId": null}}, {"offset": {"line": 620, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Internship-Projects/The-Salty-Devs/client/src/app/layout.tsx"], "sourcesContent": ["import type { <PERSON>ada<PERSON> } from 'next';\r\nimport React from 'react';\r\nimport { ThemeProvider } from 'next-themes';\r\nimport Header from '@/components/header/page';\r\nimport Footer from '@/components/footer/page';\r\nimport '../styles/globals.css';\r\n\r\nexport const metadata: Metadata = {\r\n  title: 'The Salty Devs',\r\n  description: 'The Salty Devs Blog Page',\r\n};\r\n\r\nexport default function RootLayout({\r\n  children,\r\n}: Readonly<{\r\n  children: React.ReactNode;\r\n}>) {\r\n  return (\r\n    <html lang=\"en\" suppressHydrationWarning={true}>\r\n      <body suppressHydrationWarning>\r\n        <ThemeProvider\r\n          attribute=\"class\"\r\n          defaultTheme=\"system\"\r\n          enableSystem\r\n          disableTransitionOnChange\r\n        >\r\n          <div className=\"min-h-screen flex flex-col\">\r\n            <Header />\r\n            <main className=\"flex-1 pt-20\">{children}</main>\r\n            <Footer />\r\n          </div>\r\n        </ThemeProvider>\r\n      </body>\r\n    </html>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;;;AAEA;AACA;AACA;;;;;;AAGO,MAAM,WAAqB;IAChC,OAAO;IACP,aAAa;AACf;AAEe,SAAS,WAAW,EACjC,QAAQ,EAGR;IACA,qBACE,wPAAC;QAAK,MAAK;QAAK,0BAA0B;kBACxC,cAAA,wPAAC;YAAK,wBAAwB;sBAC5B,cAAA,wPAAC,2KAAa;gBACZ,WAAU;gBACV,cAAa;gBACb,YAAY;gBACZ,yBAAyB;0BAEzB,cAAA,wPAAC;oBAAI,WAAU;;sCACb,wPAAC,yJAAM;;;;;sCACP,wPAAC;4BAAK,WAAU;sCAAgB;;;;;;sCAChC,wPAAC,yJAAM;;;;;;;;;;;;;;;;;;;;;;;;;;AAMnB", "debugId": null}}]}