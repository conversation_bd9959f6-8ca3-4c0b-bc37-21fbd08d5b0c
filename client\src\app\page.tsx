import React from 'react';
import PostCard from '@/components/postCard/page';

const mockPosts = [
  {
    slug: 'type-safe-api-with-prisma',
    title: 'Building a Type-Safe API with Prisma and tRPC',
    date: 'August 18, 2025',
    excerpt:
      "Moving beyond traditional REST. How combining Prisma's database safety with tRPC's end-to-end type safety can eliminate entire classes of bugs...",
  },
  {
    slug: 'the-new-wave-of-css',
    title: 'The New Wave of CSS',
    date: 'July 22, 2025',
    excerpt:
      'A look at modern CSS features that are changing the way we build layouts and design systems: container queries, the `:has()` selector, and cascade layers.',
  },
  {
    slug: 'server-components-in-nextjs-14',
    title: 'Server Components in Next.js 14: A Practical Guide',
    date: 'June 15, 2025',
    excerpt:
      'Moving beyond the theory. A step-by-step guide to refactoring a client-side rendered app to leverage the power of React Server Components for better performance.',
  },
];

export default function Home() {
  return (
    <main className="container mx-auto px-4 py-8">
      <div className="text-center mb-12">
        <h1 className="text-4xl md:text-6xl font-bold mb-4">
          Welcome to Our Blog
        </h1>
        <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
          Discover insights, tutorials, and thoughts on web development, design,
          and technology.
        </p>
      </div>

      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {mockPosts.map((post) => (
          <PostCard key={post.id} post={post} />
        ))}
      </div>
    </main>
  );
}
