{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Internship-Projects/The-Salty-Devs/client/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from 'clsx';\nimport { twMerge } from 'tailwind-merge';\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEO,SAAS;IAAG,IAAA,IAAA,OAAA,UAAA,QAAA,AAAG,SAAH,UAAA,OAAA,OAAA,GAAA,OAAA,MAAA;QAAG,OAAH,QAAA,SAAA,CAAA,KAAuB;;IACxC,OAAO,IAAA,mLAAO,EAAC,IAAA,0JAAI,EAAC;AACtB", "debugId": null}}, {"offset": {"line": 25, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Internship-Projects/The-Salty-Devs/client/src/components/ui/accordion.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as AccordionPrimitive from \"@radix-ui/react-accordion\"\nimport { ChevronDownIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Accordion({\n  ...props\n}: React.ComponentProps<typeof AccordionPrimitive.Root>) {\n  return <AccordionPrimitive.Root data-slot=\"accordion\" {...props} />\n}\n\nfunction AccordionItem({\n  className,\n  ...props\n}: React.ComponentProps<typeof AccordionPrimitive.Item>) {\n  return (\n    <AccordionPrimitive.Item\n      data-slot=\"accordion-item\"\n      className={cn(\"border-b last:border-b-0\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction AccordionTrigger({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof AccordionPrimitive.Trigger>) {\n  return (\n    <AccordionPrimitive.Header className=\"flex\">\n      <AccordionPrimitive.Trigger\n        data-slot=\"accordion-trigger\"\n        className={cn(\n          \"focus-visible:border-ring focus-visible:ring-ring/50 flex flex-1 items-start justify-between gap-4 rounded-md py-4 text-left text-sm font-medium transition-all outline-none hover:underline focus-visible:ring-[3px] disabled:pointer-events-none disabled:opacity-50 [&[data-state=open]>svg]:rotate-180\",\n          className\n        )}\n        {...props}\n      >\n        {children}\n        <ChevronDownIcon className=\"text-muted-foreground pointer-events-none size-4 shrink-0 translate-y-0.5 transition-transform duration-200\" />\n      </AccordionPrimitive.Trigger>\n    </AccordionPrimitive.Header>\n  )\n}\n\nfunction AccordionContent({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof AccordionPrimitive.Content>) {\n  return (\n    <AccordionPrimitive.Content\n      data-slot=\"accordion-content\"\n      className=\"data-[state=closed]:animate-accordion-up data-[state=open]:animate-accordion-down overflow-hidden text-sm\"\n      {...props}\n    >\n      <div className={cn(\"pt-0 pb-4\", className)}>{children}</div>\n    </AccordionPrimitive.Content>\n  )\n}\n\nexport { Accordion, AccordionItem, AccordionTrigger, AccordionContent }\n"], "names": [], "mappings": ";;;;;;;;;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,UAAU,KAEoC;QAFpC,EACjB,GAAG,OACkD,GAFpC;IAGjB,qBAAO,uMAAC,0LAAuB;QAAC,aAAU;QAAa,GAAG,KAAK;;;;;;AACjE;KAJS;AAMT,SAAS,cAAc,KAGgC;QAHhC,EACrB,SAAS,EACT,GAAG,OACkD,GAHhC;IAIrB,qBACE,uMAAC,0LAAuB;QACtB,aAAU;QACV,WAAW,IAAA,sIAAE,EAAC,4BAA4B;QACzC,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,iBAAiB,KAIgC;QAJhC,EACxB,SAAS,EACT,QAAQ,EACR,GAAG,OACqD,GAJhC;IAKxB,qBACE,uMAAC,4LAAyB;QAAC,WAAU;kBACnC,cAAA,uMAAC,6LAA0B;YACzB,aAAU;YACV,WAAW,IAAA,sIAAE,EACX,8SACA;YAED,GAAG,KAAK;;gBAER;8BACD,uMAAC,wPAAe;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAInC;MApBS;AAsBT,SAAS,iBAAiB,KAIgC;QAJhC,EACxB,SAAS,EACT,QAAQ,EACR,GAAG,OACqD,GAJhC;IAKxB,qBACE,uMAAC,6LAA0B;QACzB,aAAU;QACV,WAAU;QACT,GAAG,KAAK;kBAET,cAAA,uMAAC;YAAI,WAAW,IAAA,sIAAE,EAAC,aAAa;sBAAa;;;;;;;;;;;AAGnD;MAdS", "debugId": null}}, {"offset": {"line": 133, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Internship-Projects/The-Salty-Devs/client/src/components/ui/sheet.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SheetPrimitive from \"@radix-ui/react-dialog\"\nimport { XIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Sheet({ ...props }: React.ComponentProps<typeof SheetPrimitive.Root>) {\n  return <SheetPrimitive.Root data-slot=\"sheet\" {...props} />\n}\n\nfunction SheetTrigger({\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Trigger>) {\n  return <SheetPrimitive.Trigger data-slot=\"sheet-trigger\" {...props} />\n}\n\nfunction SheetClose({\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Close>) {\n  return <SheetPrimitive.Close data-slot=\"sheet-close\" {...props} />\n}\n\nfunction SheetPortal({\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Portal>) {\n  return <SheetPrimitive.Portal data-slot=\"sheet-portal\" {...props} />\n}\n\nfunction SheetOverlay({\n  className,\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Overlay>) {\n  return (\n    <SheetPrimitive.Overlay\n      data-slot=\"sheet-overlay\"\n      className={cn(\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction SheetContent({\n  className,\n  children,\n  side = \"right\",\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Content> & {\n  side?: \"top\" | \"right\" | \"bottom\" | \"left\"\n}) {\n  return (\n    <SheetPortal>\n      <SheetOverlay />\n      <SheetPrimitive.Content\n        data-slot=\"sheet-content\"\n        className={cn(\n          \"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out fixed z-50 flex flex-col gap-4 shadow-lg transition ease-in-out data-[state=closed]:duration-300 data-[state=open]:duration-500\",\n          side === \"right\" &&\n            \"data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right inset-y-0 right-0 h-full w-3/4 border-l sm:max-w-sm\",\n          side === \"left\" &&\n            \"data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left inset-y-0 left-0 h-full w-3/4 border-r sm:max-w-sm\",\n          side === \"top\" &&\n            \"data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top inset-x-0 top-0 h-auto border-b\",\n          side === \"bottom\" &&\n            \"data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom inset-x-0 bottom-0 h-auto border-t\",\n          className\n        )}\n        {...props}\n      >\n        {children}\n        <SheetPrimitive.Close className=\"ring-offset-background focus:ring-ring data-[state=open]:bg-secondary absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none\">\n          <XIcon className=\"size-4\" />\n          <span className=\"sr-only\">Close</span>\n        </SheetPrimitive.Close>\n      </SheetPrimitive.Content>\n    </SheetPortal>\n  )\n}\n\nfunction SheetHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"sheet-header\"\n      className={cn(\"flex flex-col gap-1.5 p-4\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SheetFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"sheet-footer\"\n      className={cn(\"mt-auto flex flex-col gap-2 p-4\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SheetTitle({\n  className,\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Title>) {\n  return (\n    <SheetPrimitive.Title\n      data-slot=\"sheet-title\"\n      className={cn(\"text-foreground font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SheetDescription({\n  className,\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Description>) {\n  return (\n    <SheetPrimitive.Description\n      data-slot=\"sheet-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Sheet,\n  SheetTrigger,\n  SheetClose,\n  SheetContent,\n  SheetHeader,\n  SheetFooter,\n  SheetTitle,\n  SheetDescription,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,MAAM,KAA8D;QAA9D,EAAE,GAAG,OAAyD,GAA9D;IACb,qBAAO,uMAAC,uLAAmB;QAAC,aAAU;QAAS,GAAG,KAAK;;;;;;AACzD;KAFS;AAIT,SAAS,aAAa,KAEgC;QAFhC,EACpB,GAAG,OACiD,GAFhC;IAGpB,qBAAO,uMAAC,0LAAsB;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACpE;MAJS;AAMT,SAAS,WAAW,KAEgC;QAFhC,EAClB,GAAG,OAC+C,GAFhC;IAGlB,qBAAO,uMAAC,wLAAoB;QAAC,aAAU;QAAe,GAAG,KAAK;;;;;;AAChE;MAJS;AAMT,SAAS,YAAY,KAEgC;QAFhC,EACnB,GAAG,OACgD,GAFhC;IAGnB,qBAAO,uMAAC,yLAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,aAAa,KAGgC;QAHhC,EACpB,SAAS,EACT,GAAG,OACiD,GAHhC;IAIpB,qBACE,uMAAC,0LAAsB;QACrB,aAAU;QACV,WAAW,IAAA,sIAAE,EACX,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,aAAa,KAOrB;QAPqB,EACpB,SAAS,EACT,QAAQ,EACR,OAAO,OAAO,EACd,GAAG,OAGJ,GAPqB;IAQpB,qBACE,uMAAC;;0BACC,uMAAC;;;;;0BACD,uMAAC,0LAAsB;gBACrB,aAAU;gBACV,WAAW,IAAA,sIAAE,EACX,8MACA,SAAS,WACP,oIACF,SAAS,UACP,iIACF,SAAS,SACP,4GACF,SAAS,YACP,qHACF;gBAED,GAAG,KAAK;;oBAER;kCACD,uMAAC,wLAAoB;wBAAC,WAAU;;0CAC9B,uMAAC,sNAAK;gCAAC,WAAU;;;;;;0CACjB,uMAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAKpC;MAnCS;AAqCT,SAAS,YAAY,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACnB,qBACE,uMAAC;QACC,aAAU;QACV,WAAW,IAAA,sIAAE,EAAC,6BAA6B;QAC1C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,YAAY,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACnB,qBACE,uMAAC;QACC,aAAU;QACV,WAAW,IAAA,sIAAE,EAAC,mCAAmC;QAChD,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,KAGgC;QAHhC,EAClB,SAAS,EACT,GAAG,OAC+C,GAHhC;IAIlB,qBACE,uMAAC,wLAAoB;QACnB,aAAU;QACV,WAAW,IAAA,sIAAE,EAAC,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,iBAAiB,KAGgC;QAHhC,EACxB,SAAS,EACT,GAAG,OACqD,GAHhC;IAIxB,qBACE,uMAAC,8LAA0B;QACzB,aAAU;QACV,WAAW,IAAA,sIAAE,EAAC,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MAXS", "debugId": null}}, {"offset": {"line": 345, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Internship-Projects/The-Salty-Devs/client/src/components/ui/context-menu.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as ContextMenuPrimitive from \"@radix-ui/react-context-menu\"\nimport { CheckIcon, ChevronRightIcon, CircleIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction ContextMenu({\n  ...props\n}: React.ComponentProps<typeof ContextMenuPrimitive.Root>) {\n  return <ContextMenuPrimitive.Root data-slot=\"context-menu\" {...props} />\n}\n\nfunction ContextMenuTrigger({\n  ...props\n}: React.ComponentProps<typeof ContextMenuPrimitive.Trigger>) {\n  return (\n    <ContextMenuPrimitive.Trigger data-slot=\"context-menu-trigger\" {...props} />\n  )\n}\n\nfunction ContextMenuGroup({\n  ...props\n}: React.ComponentProps<typeof ContextMenuPrimitive.Group>) {\n  return (\n    <ContextMenuPrimitive.Group data-slot=\"context-menu-group\" {...props} />\n  )\n}\n\nfunction ContextMenuPortal({\n  ...props\n}: React.ComponentProps<typeof ContextMenuPrimitive.Portal>) {\n  return (\n    <ContextMenuPrimitive.Portal data-slot=\"context-menu-portal\" {...props} />\n  )\n}\n\nfunction ContextMenuSub({\n  ...props\n}: React.ComponentProps<typeof ContextMenuPrimitive.Sub>) {\n  return <ContextMenuPrimitive.Sub data-slot=\"context-menu-sub\" {...props} />\n}\n\nfunction ContextMenuRadioGroup({\n  ...props\n}: React.ComponentProps<typeof ContextMenuPrimitive.RadioGroup>) {\n  return (\n    <ContextMenuPrimitive.RadioGroup\n      data-slot=\"context-menu-radio-group\"\n      {...props}\n    />\n  )\n}\n\nfunction ContextMenuSubTrigger({\n  className,\n  inset,\n  children,\n  ...props\n}: React.ComponentProps<typeof ContextMenuPrimitive.SubTrigger> & {\n  inset?: boolean\n}) {\n  return (\n    <ContextMenuPrimitive.SubTrigger\n      data-slot=\"context-menu-sub-trigger\"\n      data-inset={inset}\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground data-[state=open]:bg-accent data-[state=open]:text-accent-foreground flex cursor-default items-center rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <ChevronRightIcon className=\"ml-auto\" />\n    </ContextMenuPrimitive.SubTrigger>\n  )\n}\n\nfunction ContextMenuSubContent({\n  className,\n  ...props\n}: React.ComponentProps<typeof ContextMenuPrimitive.SubContent>) {\n  return (\n    <ContextMenuPrimitive.SubContent\n      data-slot=\"context-menu-sub-content\"\n      className={cn(\n        \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 min-w-[8rem] origin-(--radix-context-menu-content-transform-origin) overflow-hidden rounded-md border p-1 shadow-lg\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction ContextMenuContent({\n  className,\n  ...props\n}: React.ComponentProps<typeof ContextMenuPrimitive.Content>) {\n  return (\n    <ContextMenuPrimitive.Portal>\n      <ContextMenuPrimitive.Content\n        data-slot=\"context-menu-content\"\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-context-menu-content-available-height) min-w-[8rem] origin-(--radix-context-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md\",\n          className\n        )}\n        {...props}\n      />\n    </ContextMenuPrimitive.Portal>\n  )\n}\n\nfunction ContextMenuItem({\n  className,\n  inset,\n  variant = \"default\",\n  ...props\n}: React.ComponentProps<typeof ContextMenuPrimitive.Item> & {\n  inset?: boolean\n  variant?: \"default\" | \"destructive\"\n}) {\n  return (\n    <ContextMenuPrimitive.Item\n      data-slot=\"context-menu-item\"\n      data-inset={inset}\n      data-variant={variant}\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction ContextMenuCheckboxItem({\n  className,\n  children,\n  checked,\n  ...props\n}: React.ComponentProps<typeof ContextMenuPrimitive.CheckboxItem>) {\n  return (\n    <ContextMenuPrimitive.CheckboxItem\n      data-slot=\"context-menu-checkbox-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      checked={checked}\n      {...props}\n    >\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\n        <ContextMenuPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </ContextMenuPrimitive.ItemIndicator>\n      </span>\n      {children}\n    </ContextMenuPrimitive.CheckboxItem>\n  )\n}\n\nfunction ContextMenuRadioItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof ContextMenuPrimitive.RadioItem>) {\n  return (\n    <ContextMenuPrimitive.RadioItem\n      data-slot=\"context-menu-radio-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\n        <ContextMenuPrimitive.ItemIndicator>\n          <CircleIcon className=\"size-2 fill-current\" />\n        </ContextMenuPrimitive.ItemIndicator>\n      </span>\n      {children}\n    </ContextMenuPrimitive.RadioItem>\n  )\n}\n\nfunction ContextMenuLabel({\n  className,\n  inset,\n  ...props\n}: React.ComponentProps<typeof ContextMenuPrimitive.Label> & {\n  inset?: boolean\n}) {\n  return (\n    <ContextMenuPrimitive.Label\n      data-slot=\"context-menu-label\"\n      data-inset={inset}\n      className={cn(\n        \"text-foreground px-2 py-1.5 text-sm font-medium data-[inset]:pl-8\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction ContextMenuSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof ContextMenuPrimitive.Separator>) {\n  return (\n    <ContextMenuPrimitive.Separator\n      data-slot=\"context-menu-separator\"\n      className={cn(\"bg-border -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction ContextMenuShortcut({\n  className,\n  ...props\n}: React.ComponentProps<\"span\">) {\n  return (\n    <span\n      data-slot=\"context-menu-shortcut\"\n      className={cn(\n        \"text-muted-foreground ml-auto text-xs tracking-widest\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport {\n  ContextMenu,\n  ContextMenuTrigger,\n  ContextMenuContent,\n  ContextMenuItem,\n  ContextMenuCheckboxItem,\n  ContextMenuRadioItem,\n  ContextMenuLabel,\n  ContextMenuSeparator,\n  ContextMenuShortcut,\n  ContextMenuGroup,\n  ContextMenuPortal,\n  ContextMenuSub,\n  ContextMenuSubContent,\n  ContextMenuSubTrigger,\n  ContextMenuRadioGroup,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,YAAY,KAEoC;QAFpC,EACnB,GAAG,OACoD,GAFpC;IAGnB,qBAAO,uMAAC,gMAAyB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AACtE;KAJS;AAMT,SAAS,mBAAmB,KAEgC;QAFhC,EAC1B,GAAG,OACuD,GAFhC;IAG1B,qBACE,uMAAC,mMAA4B;QAAC,aAAU;QAAwB,GAAG,KAAK;;;;;;AAE5E;MANS;AAQT,SAAS,iBAAiB,KAEgC;QAFhC,EACxB,GAAG,OACqD,GAFhC;IAGxB,qBACE,uMAAC,iMAA0B;QAAC,aAAU;QAAsB,GAAG,KAAK;;;;;;AAExE;MANS;AAQT,SAAS,kBAAkB,KAEgC;QAFhC,EACzB,GAAG,OACsD,GAFhC;IAGzB,qBACE,uMAAC,kMAA2B;QAAC,aAAU;QAAuB,GAAG,KAAK;;;;;;AAE1E;MANS;AAQT,SAAS,eAAe,KAEgC;QAFhC,EACtB,GAAG,OACmD,GAFhC;IAGtB,qBAAO,uMAAC,+LAAwB;QAAC,aAAU;QAAoB,GAAG,KAAK;;;;;;AACzE;MAJS;AAMT,SAAS,sBAAsB,KAEgC;QAFhC,EAC7B,GAAG,OAC0D,GAFhC;IAG7B,qBACE,uMAAC,sMAA+B;QAC9B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;MATS;AAWT,SAAS,sBAAsB,KAO9B;QAP8B,EAC7B,SAAS,EACT,KAAK,EACL,QAAQ,EACR,GAAG,OAGJ,GAP8B;IAQ7B,qBACE,uMAAC,sMAA+B;QAC9B,aAAU;QACV,cAAY;QACZ,WAAW,IAAA,sIAAE,EACX,oTACA;QAED,GAAG,KAAK;;YAER;0BACD,uMAAC,2PAAgB;gBAAC,WAAU;;;;;;;;;;;;AAGlC;MAtBS;AAwBT,SAAS,sBAAsB,KAGgC;QAHhC,EAC7B,SAAS,EACT,GAAG,OAC0D,GAHhC;IAI7B,qBACE,uMAAC,sMAA+B;QAC9B,aAAU;QACV,WAAW,IAAA,sIAAE,EACX,gfACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,mBAAmB,KAGgC;QAHhC,EAC1B,SAAS,EACT,GAAG,OACuD,GAHhC;IAI1B,qBACE,uMAAC,kMAA2B;kBAC1B,cAAA,uMAAC,mMAA4B;YAC3B,aAAU;YACV,WAAW,IAAA,sIAAE,EACX,wjBACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIjB;MAhBS;AAkBT,SAAS,gBAAgB,KAQxB;QARwB,EACvB,SAAS,EACT,KAAK,EACL,UAAU,SAAS,EACnB,GAAG,OAIJ,GARwB;IASvB,qBACE,uMAAC,gMAAyB;QACxB,aAAU;QACV,cAAY;QACZ,gBAAc;QACd,WAAW,IAAA,sIAAE,EACX,+mBACA;QAED,GAAG,KAAK;;;;;;AAGf;MArBS;AAuBT,SAAS,wBAAwB,KAKgC;QALhC,EAC/B,SAAS,EACT,QAAQ,EACR,OAAO,EACP,GAAG,OAC4D,GALhC;IAM/B,qBACE,uMAAC,wMAAiC;QAChC,aAAU;QACV,WAAW,IAAA,sIAAE,EACX,gTACA;QAEF,SAAS;QACR,GAAG,KAAK;;0BAET,uMAAC;gBAAK,WAAU;0BACd,cAAA,uMAAC,yMAAkC;8BACjC,cAAA,uMAAC,kOAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGxB;;;;;;;AAGP;OAxBS;AA0BT,SAAS,qBAAqB,KAIgC;QAJhC,EAC5B,SAAS,EACT,QAAQ,EACR,GAAG,OACyD,GAJhC;IAK5B,qBACE,uMAAC,qMAA8B;QAC7B,aAAU;QACV,WAAW,IAAA,sIAAE,EACX,gTACA;QAED,GAAG,KAAK;;0BAET,uMAAC;gBAAK,WAAU;0BACd,cAAA,uMAAC,yMAAkC;8BACjC,cAAA,uMAAC,qOAAU;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGzB;;;;;;;AAGP;OAtBS;AAwBT,SAAS,iBAAiB,KAMzB;QANyB,EACxB,SAAS,EACT,KAAK,EACL,GAAG,OAGJ,GANyB;IAOxB,qBACE,uMAAC,iMAA0B;QACzB,aAAU;QACV,cAAY;QACZ,WAAW,IAAA,sIAAE,EACX,qEACA;QAED,GAAG,KAAK;;;;;;AAGf;OAlBS;AAoBT,SAAS,qBAAqB,KAGgC;QAHhC,EAC5B,SAAS,EACT,GAAG,OACyD,GAHhC;IAI5B,qBACE,uMAAC,qMAA8B;QAC7B,aAAU;QACV,WAAW,IAAA,sIAAE,EAAC,6BAA6B;QAC1C,GAAG,KAAK;;;;;;AAGf;OAXS;AAaT,SAAS,oBAAoB,KAGE;QAHF,EAC3B,SAAS,EACT,GAAG,OAC0B,GAHF;IAI3B,qBACE,uMAAC;QACC,aAAU;QACV,WAAW,IAAA,sIAAE,EACX,yDACA;QAED,GAAG,KAAK;;;;;;AAGf;OAdS", "debugId": null}}, {"offset": {"line": 668, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Internship-Projects/The-Salty-Devs/client/src/components/ui/logo.tsx"], "sourcesContent": ["\"use client\";\n\nimport { Download } from \"lucide-react\";\n\nimport { cn } from \"@/lib/utils\";\n\nimport {\n  ContextMenu,\n  ContextMenuContent,\n  ContextMenuItem,\n  ContextMenuTrigger,\n} from \"@/components/ui/context-menu\";\n\ninterface LogoProps extends React.HTMLAttributes<HTMLAnchorElement> {\n  url: string;\n  className?: string;\n  children: React.ReactNode;\n}\n\ninterface LogoImageProps extends React.ImgHTMLAttributes<HTMLImageElement> {\n  src: string;\n  alt: string;\n  className?: string;\n}\n\ninterface LogoTextProps extends React.HTMLAttributes<HTMLSpanElement> {\n  children: React.ReactNode;\n  className?: string;\n}\n\ninterface LogoBrandDownloadProps {\n  children: React.ReactNode;\n  files: Array<{\n    name: string;\n    path: string;\n    format: \"svg\" | \"png\" | \"jpg\" | \"jpeg\" | \"webp\";\n  }>;\n  className?: string;\n}\n\nconst LogoBrandDownload = ({\n  children,\n  files,\n  className,\n}: LogoBrandDownloadProps) => {\n  const handleDownload = async (file: LogoBrandDownloadProps[\"files\"][0]) => {\n    try {\n      const response = await fetch(file.path);\n      if (!response.ok) throw new Error(`Failed to fetch ${file.name}`);\n\n      const blob = await response.blob();\n      const url = window.URL.createObjectURL(blob);\n      const link = document.createElement(\"a\");\n      link.href = url;\n      link.download = file.name;\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n      window.URL.revokeObjectURL(url);\n    } catch (error) {\n      console.error(\"Failed to download file:\", error);\n    }\n  };\n\n  return (\n    <ContextMenu>\n      <ContextMenuTrigger asChild>\n        <div className={cn(\"inline-block\", className)}>{children}</div>\n      </ContextMenuTrigger>\n      <ContextMenuContent className=\"w-48\">\n        {files.map((file) => (\n          <ContextMenuItem\n            key={file.path}\n            onClick={() => handleDownload(file)}\n            className=\"cursor-pointer\"\n          >\n            <Download className=\"mr-2 h-4 w-4\" />\n            Download {file.format.toUpperCase()}\n          </ContextMenuItem>\n        ))}\n      </ContextMenuContent>\n    </ContextMenu>\n  );\n};\n\nconst Logo = ({ url, className, children, ...props }: LogoProps) => {\n  return (\n    <a\n      href={url}\n      className={cn(\"flex max-h-8 items-center gap-2\", className)}\n      {...props}\n    >\n      {children}\n    </a>\n  );\n};\n\nconst LogoImage = ({ src, alt, className, ...props }: LogoImageProps) => (\n  <img src={src} alt={alt} className={cn(\"block h-8\", className)} {...props} />\n);\n\nconst LogoImageMobile = ({ src, alt, className, ...props }: LogoImageProps) => (\n  <img\n    src={src}\n    alt={alt}\n    className={cn(\"flex h-8 md:hidden\", className)}\n    {...props}\n  />\n);\n\nconst LogoImageDesktop = ({\n  src,\n  alt,\n  className,\n  ...props\n}: LogoImageProps) => (\n  <img\n    src={src}\n    alt={alt}\n    className={cn(\"hidden h-8 md:flex\", className)}\n    {...props}\n  />\n);\n\nconst LogoText = ({ children, className, ...props }: LogoTextProps) => (\n  <span\n    className={cn(\"text-lg font-semibold tracking-tighter\", className)}\n    {...props}\n  >\n    {children}\n  </span>\n);\n\nconst LogoTextMobile = ({ children, className, ...props }: LogoTextProps) => (\n  <span\n    className={cn(\n      \"text-lg font-semibold tracking-tighter md:hidden\",\n      className,\n    )}\n    {...props}\n  >\n    {children}\n  </span>\n);\n\nconst LogoTextDesktop = ({ children, className, ...props }: LogoTextProps) => (\n  <span\n    className={cn(\n      \"hidden text-lg font-semibold tracking-tighter md:flex\",\n      className,\n    )}\n    {...props}\n  >\n    {children}\n  </span>\n);\n\nexport {\n  Logo,\n  LogoBrandDownload,\n  LogoImage,\n  LogoImageDesktop,\n  LogoImageMobile,\n  LogoText,\n  LogoTextDesktop,\n  LogoTextMobile,\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;AAEA;AAEA;AAEA;AANA;;;;;AAwCA,MAAM,oBAAoB;QAAC,EACzB,QAAQ,EACR,KAAK,EACL,SAAS,EACc;IACvB,MAAM,iBAAiB,OAAO;QAC5B,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,KAAK,IAAI;YACtC,IAAI,CAAC,SAAS,EAAE,EAAE,MAAM,IAAI,MAAM,AAAC,mBAA4B,OAAV,KAAK,IAAI;YAE9D,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,MAAM,MAAM,OAAO,GAAG,CAAC,eAAe,CAAC;YACvC,MAAM,OAAO,SAAS,aAAa,CAAC;YACpC,KAAK,IAAI,GAAG;YACZ,KAAK,QAAQ,GAAG,KAAK,IAAI;YACzB,SAAS,IAAI,CAAC,WAAW,CAAC;YAC1B,KAAK,KAAK;YACV,SAAS,IAAI,CAAC,WAAW,CAAC;YAC1B,OAAO,GAAG,CAAC,eAAe,CAAC;QAC7B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;QAC5C;IACF;IAEA,qBACE,uMAAC,uKAAW;;0BACV,uMAAC,8KAAkB;gBAAC,OAAO;0BACzB,cAAA,uMAAC;oBAAI,WAAW,IAAA,sIAAE,EAAC,gBAAgB;8BAAa;;;;;;;;;;;0BAElD,uMAAC,8KAAkB;gBAAC,WAAU;0BAC3B,MAAM,GAAG,CAAC,CAAC,qBACV,uMAAC,2KAAe;wBAEd,SAAS,IAAM,eAAe;wBAC9B,WAAU;;0CAEV,uMAAC,mOAAQ;gCAAC,WAAU;;;;;;4BAAiB;4BAC3B,KAAK,MAAM,CAAC,WAAW;;uBAL5B,KAAK,IAAI;;;;;;;;;;;;;;;;AAW1B;KA3CM;AA6CN,MAAM,OAAO;QAAC,EAAE,GAAG,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAkB;IAC7D,qBACE,uMAAC;QACC,MAAM;QACN,WAAW,IAAA,sIAAE,EAAC,mCAAmC;QAChD,GAAG,KAAK;kBAER;;;;;;AAGP;MAVM;AAYN,MAAM,YAAY;QAAC,EAAE,GAAG,EAAE,GAAG,EAAE,SAAS,EAAE,GAAG,OAAuB;yBAClE,uMAAC;QAAI,KAAK;QAAK,KAAK;QAAK,WAAW,IAAA,sIAAE,EAAC,aAAa;QAAa,GAAG,KAAK;;;;;;;MADrE;AAIN,MAAM,kBAAkB;QAAC,EAAE,GAAG,EAAE,GAAG,EAAE,SAAS,EAAE,GAAG,OAAuB;yBACxE,uMAAC;QACC,KAAK;QACL,KAAK;QACL,WAAW,IAAA,sIAAE,EAAC,sBAAsB;QACnC,GAAG,KAAK;;;;;;;MALP;AASN,MAAM,mBAAmB;QAAC,EACxB,GAAG,EACH,GAAG,EACH,SAAS,EACT,GAAG,OACY;yBACf,uMAAC;QACC,KAAK;QACL,KAAK;QACL,WAAW,IAAA,sIAAE,EAAC,sBAAsB;QACnC,GAAG,KAAK;;;;;;;MAVP;AAcN,MAAM,WAAW;QAAC,EAAE,QAAQ,EAAE,SAAS,EAAE,GAAG,OAAsB;yBAChE,uMAAC;QACC,WAAW,IAAA,sIAAE,EAAC,0CAA0C;QACvD,GAAG,KAAK;kBAER;;;;;;;MALC;AASN,MAAM,iBAAiB;QAAC,EAAE,QAAQ,EAAE,SAAS,EAAE,GAAG,OAAsB;yBACtE,uMAAC;QACC,WAAW,IAAA,sIAAE,EACX,oDACA;QAED,GAAG,KAAK;kBAER;;;;;;;MARC;AAYN,MAAM,kBAAkB;QAAC,EAAE,QAAQ,EAAE,SAAS,EAAE,GAAG,OAAsB;yBACvE,uMAAC;QACC,WAAW,IAAA,sIAAE,EACX,yDACA;QAED,GAAG,KAAK;kBAER;;;;;;;MARC", "debugId": null}}]}